namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    partial class StaticRockMechanicsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(StaticRockMechanicsForm));
            lblTitle = new Label();
            lblWelcome = new Label();
            btnBack = new Button();
            btnLogout = new Button();
            btnEmergencyExit = new Button();
            btnEnhancedAnalysis = new Button();
            btnViewComparison = new Button();
            pnlParameters = new Panel();
            grpVs = new GroupBox();
            rbVelocityDts = new RadioButton();
            rbVelocityVs = new RadioButton();
            grpVp = new GroupBox();
            rbVelocityDt = new RadioButton();
            rbVelocityVp = new RadioButton();
            grpDensity = new GroupBox();
            rbDensityRhob = new RadioButton();
            rbDensityRho = new RadioButton();
            lblCalculationResult = new Label();
            btnCalculate = new Button();
            txtVs = new TextBox();
            lblVs = new Label();
            txtVp = new TextBox();
            lblVp = new Label();
            txtDensity = new TextBox();
            lblDensity = new Label();
            lblParametersTitle = new Label();
            pnlChart = new Panel();
            btnSaveCurve = new Button();
            btnReset = new Button();
            btnGenerateCurve = new Button();
            lblChartTitle = new Label();
            pnlData = new Panel();
            dgvMechanicsData = new DataGridView();
            btnExport = new Button();
            btnImport = new Button();
            lblDataTitle = new Label();
            pnlParameters.SuspendLayout();
            grpVs.SuspendLayout();
            grpVp.SuspendLayout();
            grpDensity.SuspendLayout();
            pnlChart.SuspendLayout();
            pnlData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMechanicsData).BeginInit();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.BackColor = Color.FromArgb(45, 45, 45);
            resources.ApplyResources(lblTitle, "lblTitle");
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.Name = "lblTitle";
            // 
            // lblWelcome
            // 
            resources.ApplyResources(lblWelcome, "lblWelcome");
            lblWelcome.ForeColor = Color.LightGray;
            lblWelcome.Name = "lblWelcome";
            // 
            // btnBack
            // 
            btnBack.BackColor = Color.FromArgb(50, 50, 50);
            btnBack.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnBack, "btnBack");
            btnBack.ForeColor = Color.LightSkyBlue;
            btnBack.Name = "btnBack";
            btnBack.UseVisualStyleBackColor = false;
            btnBack.Click += btnBack_Click;
            // 
            // btnLogout
            // 
            btnLogout.BackColor = Color.FromArgb(50, 50, 50);
            btnLogout.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnLogout, "btnLogout");
            btnLogout.ForeColor = Color.LightSkyBlue;
            btnLogout.Name = "btnLogout";
            btnLogout.UseVisualStyleBackColor = false;
            btnLogout.Click += btnLogout_Click;
            // 
            // btnEmergencyExit
            // 
            btnEmergencyExit.BackColor = Color.FromArgb(50, 50, 50);
            btnEmergencyExit.FlatAppearance.BorderColor = Color.Red;
            resources.ApplyResources(btnEmergencyExit, "btnEmergencyExit");
            btnEmergencyExit.ForeColor = Color.Red;
            btnEmergencyExit.Name = "btnEmergencyExit";
            btnEmergencyExit.UseVisualStyleBackColor = false;
            btnEmergencyExit.Click += btnEmergencyExit_Click;
            // 
            // btnEnhancedAnalysis
            // 
            btnEnhancedAnalysis.BackColor = Color.FromArgb(0, 120, 215);
            btnEnhancedAnalysis.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnEnhancedAnalysis, "btnEnhancedAnalysis");
            btnEnhancedAnalysis.ForeColor = Color.White;
            btnEnhancedAnalysis.Name = "btnEnhancedAnalysis";
            btnEnhancedAnalysis.UseVisualStyleBackColor = false;
            btnEnhancedAnalysis.Click += btnEnhancedAnalysis_Click;
            // 
            // btnViewComparison
            // 
            btnViewComparison.BackColor = Color.FromArgb(0, 120, 215);
            btnViewComparison.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnViewComparison, "btnViewComparison");
            btnViewComparison.ForeColor = Color.White;
            btnViewComparison.Name = "btnViewComparison";
            btnViewComparison.UseVisualStyleBackColor = false;
            btnViewComparison.Click += btnViewComparison_Click;
            // 
            // pnlParameters
            // 
            pnlParameters.BackColor = Color.FromArgb(45, 45, 45);
            pnlParameters.BorderStyle = BorderStyle.FixedSingle;
            pnlParameters.Controls.Add(btnViewComparison);
            pnlParameters.Controls.Add(grpVs);
            pnlParameters.Controls.Add(grpVp);
            pnlParameters.Controls.Add(grpDensity);
            pnlParameters.Controls.Add(lblCalculationResult);
            pnlParameters.Controls.Add(btnCalculate);
            pnlParameters.Controls.Add(txtVs);
            pnlParameters.Controls.Add(lblVs);
            pnlParameters.Controls.Add(txtVp);
            pnlParameters.Controls.Add(lblVp);
            pnlParameters.Controls.Add(txtDensity);
            pnlParameters.Controls.Add(lblDensity);
            pnlParameters.Controls.Add(lblParametersTitle);
            resources.ApplyResources(pnlParameters, "pnlParameters");
            pnlParameters.Name = "pnlParameters";
            // 
            // grpVs
            // 
            grpVs.Controls.Add(rbVelocityDts);
            grpVs.Controls.Add(rbVelocityVs);
            grpVs.ForeColor = Color.WhiteSmoke;
            resources.ApplyResources(grpVs, "grpVs");
            grpVs.Name = "grpVs";
            grpVs.TabStop = false;
            // 
            // rbVelocityDts
            // 
            resources.ApplyResources(rbVelocityDts, "rbVelocityDts");
            rbVelocityDts.Name = "rbVelocityDts";
            rbVelocityDts.UseVisualStyleBackColor = true;
            // 
            // rbVelocityVs
            // 
            resources.ApplyResources(rbVelocityVs, "rbVelocityVs");
            rbVelocityVs.Checked = true;
            rbVelocityVs.Name = "rbVelocityVs";
            rbVelocityVs.TabStop = true;
            rbVelocityVs.UseVisualStyleBackColor = true;
            // 
            // grpVp
            // 
            grpVp.Controls.Add(rbVelocityDt);
            grpVp.Controls.Add(rbVelocityVp);
            grpVp.ForeColor = Color.WhiteSmoke;
            resources.ApplyResources(grpVp, "grpVp");
            grpVp.Name = "grpVp";
            grpVp.TabStop = false;
            // 
            // rbVelocityDt
            // 
            resources.ApplyResources(rbVelocityDt, "rbVelocityDt");
            rbVelocityDt.Name = "rbVelocityDt";
            rbVelocityDt.UseVisualStyleBackColor = true;
            // 
            // rbVelocityVp
            // 
            resources.ApplyResources(rbVelocityVp, "rbVelocityVp");
            rbVelocityVp.Checked = true;
            rbVelocityVp.Name = "rbVelocityVp";
            rbVelocityVp.TabStop = true;
            rbVelocityVp.UseVisualStyleBackColor = true;
            // 
            // grpDensity
            // 
            grpDensity.Controls.Add(rbDensityRhob);
            grpDensity.Controls.Add(rbDensityRho);
            grpDensity.ForeColor = Color.WhiteSmoke;
            resources.ApplyResources(grpDensity, "grpDensity");
            grpDensity.Name = "grpDensity";
            grpDensity.TabStop = false;
            // 
            // rbDensityRhob
            // 
            resources.ApplyResources(rbDensityRhob, "rbDensityRhob");
            rbDensityRhob.Name = "rbDensityRhob";
            rbDensityRhob.UseVisualStyleBackColor = true;
            // 
            // rbDensityRho
            // 
            resources.ApplyResources(rbDensityRho, "rbDensityRho");
            rbDensityRho.Checked = true;
            rbDensityRho.Name = "rbDensityRho";
            rbDensityRho.TabStop = true;
            rbDensityRho.UseVisualStyleBackColor = true;
            // 
            // lblCalculationResult
            // 
            resources.ApplyResources(lblCalculationResult, "lblCalculationResult");
            lblCalculationResult.ForeColor = Color.Yellow;
            lblCalculationResult.Name = "lblCalculationResult";
            // 
            // btnCalculate
            // 
            btnCalculate.BackColor = Color.FromArgb(50, 50, 50);
            btnCalculate.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnCalculate, "btnCalculate");
            btnCalculate.ForeColor = Color.LightSkyBlue;
            btnCalculate.Name = "btnCalculate";
            btnCalculate.UseVisualStyleBackColor = false;
            btnCalculate.Click += btnCalculate_Click;
            // 
            // txtVs
            // 
            txtVs.BackColor = Color.FromArgb(60, 60, 60);
            txtVs.BorderStyle = BorderStyle.FixedSingle;
            resources.ApplyResources(txtVs, "txtVs");
            txtVs.ForeColor = Color.White;
            txtVs.Name = "txtVs";
            // 
            // lblVs
            // 
            resources.ApplyResources(lblVs, "lblVs");
            lblVs.ForeColor = Color.White;
            lblVs.Name = "lblVs";
            // 
            // txtVp
            // 
            txtVp.BackColor = Color.FromArgb(60, 60, 60);
            txtVp.BorderStyle = BorderStyle.FixedSingle;
            resources.ApplyResources(txtVp, "txtVp");
            txtVp.ForeColor = Color.White;
            txtVp.Name = "txtVp";
            // 
            // lblVp
            // 
            resources.ApplyResources(lblVp, "lblVp");
            lblVp.ForeColor = Color.White;
            lblVp.Name = "lblVp";
            // 
            // txtDensity
            // 
            txtDensity.BackColor = Color.FromArgb(60, 60, 60);
            txtDensity.BorderStyle = BorderStyle.FixedSingle;
            resources.ApplyResources(txtDensity, "txtDensity");
            txtDensity.ForeColor = Color.White;
            txtDensity.Name = "txtDensity";
            // 
            // lblDensity
            // 
            resources.ApplyResources(lblDensity, "lblDensity");
            lblDensity.ForeColor = Color.White;
            lblDensity.Name = "lblDensity";
            // 
            // lblParametersTitle
            // 
            resources.ApplyResources(lblParametersTitle, "lblParametersTitle");
            lblParametersTitle.ForeColor = Color.White;
            lblParametersTitle.Name = "lblParametersTitle";
            // 
            // pnlChart
            // 
            pnlChart.BackColor = Color.FromArgb(45, 45, 45);
            pnlChart.BorderStyle = BorderStyle.FixedSingle;
            pnlChart.Controls.Add(btnSaveCurve);
            pnlChart.Controls.Add(btnReset);
            pnlChart.Controls.Add(btnGenerateCurve);
            pnlChart.Controls.Add(lblChartTitle);
            resources.ApplyResources(pnlChart, "pnlChart");
            pnlChart.Name = "pnlChart";
            // 
            // btnSaveCurve
            // 
            btnSaveCurve.BackColor = Color.FromArgb(50, 50, 50);
            btnSaveCurve.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnSaveCurve, "btnSaveCurve");
            btnSaveCurve.ForeColor = Color.LightSkyBlue;
            btnSaveCurve.Name = "btnSaveCurve";
            btnSaveCurve.UseVisualStyleBackColor = false;
            // 
            // btnReset
            // 
            btnReset.BackColor = Color.FromArgb(50, 50, 50);
            btnReset.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnReset, "btnReset");
            btnReset.ForeColor = Color.LightSkyBlue;
            btnReset.Name = "btnReset";
            btnReset.UseVisualStyleBackColor = false;
            btnReset.Click += btnClearData_Click;
            // 
            // btnGenerateCurve
            // 
            btnGenerateCurve.BackColor = Color.FromArgb(50, 50, 50);
            btnGenerateCurve.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnGenerateCurve, "btnGenerateCurve");
            btnGenerateCurve.ForeColor = Color.LightSkyBlue;
            btnGenerateCurve.Name = "btnGenerateCurve";
            btnGenerateCurve.UseVisualStyleBackColor = false;
            // 
            // lblChartTitle
            // 
            resources.ApplyResources(lblChartTitle, "lblChartTitle");
            lblChartTitle.ForeColor = Color.White;
            lblChartTitle.Name = "lblChartTitle";
            // 
            // pnlData
            // 
            pnlData.BackColor = Color.FromArgb(45, 45, 45);
            pnlData.BorderStyle = BorderStyle.FixedSingle;
            pnlData.Controls.Add(dgvMechanicsData);
            pnlData.Controls.Add(btnExport);
            pnlData.Controls.Add(btnImport);
            pnlData.Controls.Add(lblDataTitle);
            resources.ApplyResources(pnlData, "pnlData");
            pnlData.Name = "pnlData";
            // 
            // dgvMechanicsData
            // 
            dgvMechanicsData.BackgroundColor = Color.FromArgb(60, 60, 60);
            dgvMechanicsData.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            resources.ApplyResources(dgvMechanicsData, "dgvMechanicsData");
            dgvMechanicsData.Name = "dgvMechanicsData";
            dgvMechanicsData.RowTemplate.Height = 25;
            // 
            // btnExport
            // 
            btnExport.BackColor = Color.FromArgb(50, 50, 50);
            btnExport.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnExport, "btnExport");
            btnExport.ForeColor = Color.LightSkyBlue;
            btnExport.Name = "btnExport";
            btnExport.UseVisualStyleBackColor = false;
            btnExport.Click += btnExportData_Click;
            // 
            // btnImport
            // 
            btnImport.BackColor = Color.FromArgb(50, 50, 50);
            btnImport.FlatAppearance.BorderColor = Color.Cyan;
            resources.ApplyResources(btnImport, "btnImport");
            btnImport.ForeColor = Color.LightSkyBlue;
            btnImport.Name = "btnImport";
            btnImport.UseVisualStyleBackColor = false;
            btnImport.Click += btnImportData_Click;
            // 
            // lblDataTitle
            // 
            resources.ApplyResources(lblDataTitle, "lblDataTitle");
            lblDataTitle.ForeColor = Color.White;
            lblDataTitle.Name = "lblDataTitle";
            // 
            // StaticRockMechanicsForm
            // 
            resources.ApplyResources(this, "$this");
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(33, 33, 33);
            Controls.Add(pnlChart);
            Controls.Add(pnlData);
            Controls.Add(pnlParameters);
            Controls.Add(btnEnhancedAnalysis);
            Controls.Add(btnEmergencyExit);
            Controls.Add(btnLogout);
            Controls.Add(btnBack);
            Controls.Add(lblWelcome);
            Controls.Add(lblTitle);
            ForeColor = Color.White;
            Name = "StaticRockMechanicsForm";
            FormClosing += StaticRockMechanicsForm_FormClosing;
            Resize += StaticRockMechanicsForm_Resize;
            pnlParameters.ResumeLayout(false);
            pnlParameters.PerformLayout();
            grpVs.ResumeLayout(false);
            grpVs.PerformLayout();
            grpVp.ResumeLayout(false);
            grpVp.PerformLayout();
            grpDensity.ResumeLayout(false);
            grpDensity.PerformLayout();
            pnlChart.ResumeLayout(false);
            pnlChart.PerformLayout();
            pnlData.ResumeLayout(false);
            pnlData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMechanicsData).EndInit();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Label lblWelcome;
        private System.Windows.Forms.Button btnBack;
        private System.Windows.Forms.Button btnLogout;
        private System.Windows.Forms.Button btnEmergencyExit;
        private System.Windows.Forms.Button btnEnhancedAnalysis;
        private System.Windows.Forms.Button btnViewComparison;
        private System.Windows.Forms.Panel pnlParameters;
        private System.Windows.Forms.GroupBox grpVs;
        private System.Windows.Forms.RadioButton rbVelocityDts;
        private System.Windows.Forms.RadioButton rbVelocityVs;
        private System.Windows.Forms.GroupBox grpVp;
        private System.Windows.Forms.RadioButton rbVelocityDt;
        private System.Windows.Forms.RadioButton rbVelocityVp;
        private System.Windows.Forms.GroupBox grpDensity;
        private System.Windows.Forms.RadioButton rbDensityRhob;
        private System.Windows.Forms.RadioButton rbDensityRho;
        private System.Windows.Forms.Label lblCalculationResult;
        private System.Windows.Forms.Button btnCalculate;
        private System.Windows.Forms.TextBox txtVs;
        private System.Windows.Forms.Label lblVs;
        private System.Windows.Forms.TextBox txtVp;
        private System.Windows.Forms.Label lblVp;
        private System.Windows.Forms.TextBox txtDensity;
        private System.Windows.Forms.Label lblDensity;
        private System.Windows.Forms.Label lblParametersTitle;
        private System.Windows.Forms.Panel pnlChart;
        private System.Windows.Forms.DataVisualization.Charting.Chart chartBrittleness;
        private System.Windows.Forms.Button btnSaveCurve;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Button btnGenerateCurve;
        private System.Windows.Forms.Label lblChartTitle;
        private System.Windows.Forms.Panel pnlData;
        private System.Windows.Forms.DataGridView dgvMechanicsData;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.Label lblDataTitle;
    }
}
