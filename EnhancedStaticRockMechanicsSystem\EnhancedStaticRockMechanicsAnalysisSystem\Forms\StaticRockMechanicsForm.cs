using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsAnalysisSystem.Core;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;
using Newtonsoft.Json;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    public partial class StaticRockMechanicsForm : Form
    {
        private DataTable mechanicsData;
        private List<RockMechanicsDataPoint> dataPoints;
        private RockMechanicsCalculator calculator;

        // 自适应缩放相关字段
        private readonly Size originalFormSize = new Size(1400, 900);
        private readonly Dictionary<Control, Rectangle> originalControlBounds = new Dictionary<Control, Rectangle>();
        private readonly Dictionary<Control, Font> originalControlFonts = new Dictionary<Control, Font>();

        // 对比数据管理器 - 暂时注释掉，使用简化版本
        // private UnifiedComparisonDataManager comparisonDataManager;

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeData();
            InitializeChart();
            InitializeScaling();
            // InitializeComparisonManager(); // 暂时注释掉
            BindUnitSelectionEvents();
            UpdateParameterLabels();
        }

        private void InitializeData()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("序号", typeof(int));
            mechanicsData.Columns.Add("密度(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度(m/s)", typeof(double));
            mechanicsData.Columns.Add("杨氏模量(GPa)", typeof(double));
            mechanicsData.Columns.Add("泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数", typeof(double));

            dataPoints = new List<RockMechanicsDataPoint>();
            calculator = new RockMechanicsCalculator();

            // 绑定数据表到DataGridView
            dgvMechanicsData.DataSource = mechanicsData;
            dgvMechanicsData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
        }

        private void InitializeChart()
        {
            // 检查图表控件是否存在
            if (chartBrittleness != null)
            {
                chartBrittleness.Series.Clear();
                chartBrittleness.ChartAreas.Clear();

                // 创建图表区域
                ChartArea chartArea = new ChartArea("MainArea");
                chartArea.AxisX.Title = "数据点";
                chartArea.AxisY.Title = "脆性指数";
                chartArea.BackColor = Color.FromArgb(45, 45, 45);
                chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                chartArea.AxisX.TitleForeColor = Color.White;
                chartArea.AxisY.TitleForeColor = Color.White;
                chartBrittleness.ChartAreas.Add(chartArea);

                // 创建数据系列
                Series series = new Series("脆性指数");
                series.ChartType = SeriesChartType.Line;
                series.Color = Color.FromArgb(0, 120, 215);
                series.BorderWidth = 2;
                series.MarkerStyle = MarkerStyle.Circle;
                series.MarkerSize = 6;
                series.MarkerColor = Color.FromArgb(0, 120, 215);
                chartBrittleness.Series.Add(series);

                chartBrittleness.BackColor = Color.FromArgb(33, 33, 33);
            }
        }

        // 暂时注释掉，使用简化版本
        // private void InitializeComparisonManager()
        // {
        //     try
        //     {
        //         comparisonDataManager = new UnifiedComparisonDataManager();
        //         LoggingService.Instance.Info("对比数据管理器初始化成功");
        //     }
        //     catch (Exception ex)
        //     {
        //         LoggingService.Instance.Error($"对比数据管理器初始化失败: {ex.Message}");
        //     }
        // }

        private void BindUnitSelectionEvents()
        {
            try
            {
                // 绑定密度单位选择事件
                rbDensityRho.CheckedChanged += UnitSelection_CheckedChanged;
                rbDensityRhob.CheckedChanged += UnitSelection_CheckedChanged;

                // 绑定纵波速度单位选择事件
                rbVelocityDt.CheckedChanged += UnitSelection_CheckedChanged;
                rbVelocityVp.CheckedChanged += UnitSelection_CheckedChanged;

                // 绑定横波速度单位选择事件
                rbVelocityDts.CheckedChanged += UnitSelection_CheckedChanged;
                rbVelocityVs.CheckedChanged += UnitSelection_CheckedChanged;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"绑定单位选择事件失败: {ex.Message}");
            }
        }

        private void UnitSelection_CheckedChanged(object sender, EventArgs e)
        {
            if (sender is RadioButton rb && rb.Checked)
            {
                UpdateParameterLabels();
            }
        }

        private void UpdateParameterLabels()
        {
            try
            {
                // 更新密度标签
                if (rbDensityRho.Checked)
                {
                    lblDensity.Text = "密度 ρ (g/cm³):";
                }
                else if (rbDensityRhob.Checked)
                {
                    lblDensity.Text = "密度 RHOB (g/cm³):";
                }

                // 更新纵波速度标签
                if (rbVelocityDt.Checked)
                {
                    lblVp.Text = "纵波时差 DT (μs/m):";
                }
                else if (rbVelocityVp.Checked)
                {
                    lblVp.Text = "纵波速度 Vp (m/s):";
                }

                // 更新横波速度标签
                if (rbVelocityDts.Checked)
                {
                    lblVs.Text = "横波时差 DTS (μs/m):";
                }
                else if (rbVelocityVs.Checked)
                {
                    lblVs.Text = "横波速度 Vs (m/s):";
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"更新参数标签失败: {ex.Message}");
            }
        }

        private void btnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                double density = Convert.ToDouble(txtDensity.Text);
                double vp = Convert.ToDouble(txtVp.Text);
                double vs = Convert.ToDouble(txtVs.Text);

                var result = calculator.CalculateBrittleness(density, vp, vs);

                // 添加到数据表
                int rowIndex = mechanicsData.Rows.Count + 1;
                mechanicsData.Rows.Add(rowIndex, density, vp, vs,
                    result.YoungModulus, result.PoissonRatio, result.BrittlenessIndex);

                // 添加到数据点列表
                var dataPoint = new RockMechanicsDataPoint
                {
                    Density = density,
                    VpVelocity = vp,
                    VsVelocity = vs,
                    YoungModulus = result.YoungModulus,
                    PoissonRatio = result.PoissonRatio,
                    BrittlenessIndex = result.BrittlenessIndex
                };
                dataPoints.Add(dataPoint);

                // 更新图表
                UpdateChart();

                // 显示计算结果
                lblCalculationResult.Text = $"计算完成！脆性指数: {result.BrittlenessIndex:F3}";

                // 清空输入框
                ClearInputs();

                LoggingService.Instance.Info($"计算脆性指数: 密度={density}, Vp={vp}, Vs={vs}, 结果={result.BrittlenessIndex:F3}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"计算脆性指数失败: {ex.Message}");
            }
        }

        private void UpdateChart()
        {
            // 检查图表控件和系列是否存在
            if (chartBrittleness != null && chartBrittleness.Series.Count > 0)
            {
                chartBrittleness.Series["脆性指数"].Points.Clear();

                for (int i = 0; i < dataPoints.Count; i++)
                {
                    chartBrittleness.Series["脆性指数"].Points.AddXY(i + 1, dataPoints[i].BrittlenessIndex);
                }

                chartBrittleness.Invalidate();
            }
        }

        private void ClearInputs()
        {
            txtDensity.Clear();
            txtVp.Clear();
            txtVs.Clear();
            txtDensity.Focus();
        }

        private void btnClearData_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要清空所有数据吗？", "确认清空",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                mechanicsData.Clear();
                dataPoints.Clear();
                if (chartBrittleness != null && chartBrittleness.Series.Count > 0)
                {
                    chartBrittleness.Series["脆性指数"].Points.Clear();
                }
                lblCalculationResult.Text = "数据已清空";
                LoggingService.Instance.Info("清空所有计算数据");
            }
        }

        private void btnImportData_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "Excel文件|*.xlsx;*.xls|CSV文件|*.csv|所有文件|*.*";
                openFileDialog.Title = "选择要导入的数据文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 这里可以添加数据导入逻辑
                    MessageBox.Show("数据导入功能正在开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"导入数据失败: {ex.Message}");
            }
        }

        private void btnExportData_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件|*.xlsx|CSV文件|*.csv";
                saveFileDialog.Title = "保存数据文件";
                saveFileDialog.FileName = $"静态岩石力学参数分析结果_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 这里可以添加数据导出逻辑
                    MessageBox.Show("数据导出功能正在开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"导出数据失败: {ex.Message}");
            }
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (mechanicsData.Rows.Count > 0)
            {
                DialogResult result = MessageBox.Show("当前有未保存的数据，确定要关闭吗？", "确认关闭",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            LoggingService.Instance.Info("关闭静态岩石力学参数分析窗体");
        }

        // 添加缺失的事件处理方法
        private void btnLogout_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要退出登录吗？", "确认退出",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void btnEmergencyExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private async void btnViewComparison_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("开始查看对比图");

                if (comparisonDataManager == null)
                {
                    InitializeComparisonManager();
                }

                // 从标准位置加载对比数据
                var dataSets = await comparisonDataManager.LoadFromStandardLocations();

                if (dataSets.Count > 0)
                {
                    // 显示增强的对比图窗体
                    var enhancedForm = new EnhancedComparisonChartForm();
                    enhancedForm.LoadComparisonData(dataSets);
                    enhancedForm.ShowDialog();

                    LoggingService.Instance.Info($"对比图显示完成，共显示 {dataSets.Count} 个数据集");
                }
                else
                {
                    // 如果没有找到数据，提供手动选择文件的选项
                    var result = MessageBox.Show("没有找到对比数据！\n\n是否要手动选择对比数据文件？",
                        "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        var selectedFiles = comparisonDataManager.ShowSmartFileDialog();
                        if (selectedFiles.Length > 0)
                        {
                            var manualDataSets = await comparisonDataManager.LoadComparisonDataFromMultipleSources(selectedFiles);
                            if (manualDataSets.Count > 0)
                            {
                                var enhancedForm = new EnhancedComparisonChartForm();
                                enhancedForm.LoadComparisonData(manualDataSets);
                                enhancedForm.ShowDialog();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"查看对比图失败: {ex.Message}");
                MessageBox.Show($"查看对比图失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnEnhancedAnalysis_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("启动增强分析算法功能");

                if (dataPoints.Count == 0)
                {
                    MessageBox.Show("请先计算一些数据点，然后再进行增强分析。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 执行增强分析
                var analysisResult = await PerformEnhancedAnalysis();

                // 显示分析结果
                var resultForm = new Form()
                {
                    Text = "增强分析结果",
                    Size = new Size(600, 500),
                    StartPosition = FormStartPosition.CenterParent,
                    BackColor = Color.FromArgb(33, 33, 33),
                    ForeColor = Color.White
                };

                var textBox = new TextBox()
                {
                    Multiline = true,
                    ScrollBars = ScrollBars.Vertical,
                    Dock = DockStyle.Fill,
                    BackColor = Color.FromArgb(60, 60, 60),
                    ForeColor = Color.White,
                    Font = new Font("Microsoft YaHei UI", 10),
                    Text = analysisResult,
                    ReadOnly = true
                };

                resultForm.Controls.Add(textBox);
                resultForm.ShowDialog();

                LoggingService.Instance.Info("增强分析算法执行完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动增强分析功能失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"启动增强分析功能失败: {ex.Message}");
            }
        }

        private async Task<string> PerformEnhancedAnalysis()
        {
            return await Task.Run(() =>
            {
                var result = new System.Text.StringBuilder();
                result.AppendLine("=== 增强版静态岩石力学参数分析报告 ===\n");

                // 基础统计分析
                result.AppendLine("【基础统计分析】");
                result.AppendLine($"数据点总数: {dataPoints.Count}");
                result.AppendLine($"脆性指数范围: {dataPoints.Min(p => p.BrittlenessIndex):F2} - {dataPoints.Max(p => p.BrittlenessIndex):F2}");
                result.AppendLine($"脆性指数平均值: {dataPoints.Average(p => p.BrittlenessIndex):F2}");
                result.AppendLine($"杨氏模量范围: {dataPoints.Min(p => p.YoungModulus):F2} - {dataPoints.Max(p => p.YoungModulus):F2} GPa");
                result.AppendLine($"泊松比范围: {dataPoints.Min(p => p.PoissonRatio):F3} - {dataPoints.Max(p => p.PoissonRatio):F3}");
                result.AppendLine();

                // 岩石分类分析
                result.AppendLine("【岩石脆性分类分析】");
                var brittleCount = dataPoints.Count(p => p.BrittlenessIndex > 60);
                var moderateCount = dataPoints.Count(p => p.BrittlenessIndex >= 40 && p.BrittlenessIndex <= 60);
                var ductileCount = dataPoints.Count(p => p.BrittlenessIndex < 40);

                result.AppendLine($"高脆性岩石 (BI > 60): {brittleCount} 个 ({(double)brittleCount / dataPoints.Count * 100:F1}%)");
                result.AppendLine($"中等脆性岩石 (40 ≤ BI ≤ 60): {moderateCount} 个 ({(double)moderateCount / dataPoints.Count * 100:F1}%)");
                result.AppendLine($"低脆性岩石 (BI < 40): {ductileCount} 个 ({(double)ductileCount / dataPoints.Count * 100:F1}%)");
                result.AppendLine();

                // 相关性分析
                result.AppendLine("【参数相关性分析】");
                var densityBICorr = CalculateCorrelation(dataPoints.Select(p => p.Density).ToArray(),
                    dataPoints.Select(p => p.BrittlenessIndex).ToArray());
                var vpBICorr = CalculateCorrelation(dataPoints.Select(p => p.VpVelocity).ToArray(),
                    dataPoints.Select(p => p.BrittlenessIndex).ToArray());
                var vsBICorr = CalculateCorrelation(dataPoints.Select(p => p.VsVelocity).ToArray(),
                    dataPoints.Select(p => p.BrittlenessIndex).ToArray());

                result.AppendLine($"密度与脆性指数相关系数: {densityBICorr:F3}");
                result.AppendLine($"纵波速度与脆性指数相关系数: {vpBICorr:F3}");
                result.AppendLine($"横波速度与脆性指数相关系数: {vsBICorr:F3}");
                result.AppendLine();

                // 建议和结论
                result.AppendLine("【分析建议】");
                if (brittleCount > dataPoints.Count * 0.6)
                {
                    result.AppendLine("• 该区域岩石整体表现为高脆性特征，适合压裂改造");
                }
                else if (ductileCount > dataPoints.Count * 0.6)
                {
                    result.AppendLine("• 该区域岩石整体表现为低脆性特征，压裂改造效果可能有限");
                }
                else
                {
                    result.AppendLine("• 该区域岩石脆性特征分布较为均匀，建议分层分段压裂");
                }

                result.AppendLine($"• 分析完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                return result.ToString();
            });
        }

        private double CalculateCorrelation(double[] x, double[] y)
        {
            if (x.Length != y.Length || x.Length == 0)
                return 0;

            double meanX = x.Average();
            double meanY = y.Average();

            double numerator = x.Zip(y, (xi, yi) => (xi - meanX) * (yi - meanY)).Sum();
            double denominatorX = Math.Sqrt(x.Sum(xi => Math.Pow(xi - meanX, 2)));
            double denominatorY = Math.Sqrt(y.Sum(yi => Math.Pow(yi - meanY, 2)));

            if (denominatorX == 0 || denominatorY == 0)
                return 0;

            return numerator / (denominatorX * denominatorY);
        }

        #region 自适应缩放功能

        /// <summary>
        /// 初始化缩放功能，记录原始控件位置和字体
        /// </summary>
        private void InitializeScaling()
        {
            // 记录所有控件的原始位置和字体
            RecordOriginalBounds(this);
        }

        /// <summary>
        /// 递归记录控件的原始位置和字体
        /// </summary>
        private void RecordOriginalBounds(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                originalControlBounds[control] = control.Bounds;
                originalControlFonts[control] = control.Font;

                if (control.HasChildren)
                {
                    RecordOriginalBounds(control);
                }
            }
        }

        /// <summary>
        /// 窗体大小改变事件处理
        /// </summary>
        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            if (originalControlBounds.Count > 0)
            {
                ResizeControls(this);
            }
        }

        /// <summary>
        /// 递归调整控件大小和位置
        /// </summary>
        private void ResizeControls(Control parent)
        {
            float scaleX = (float)this.ClientSize.Width / originalFormSize.Width;
            float scaleY = (float)this.ClientSize.Height / originalFormSize.Height;

            foreach (Control control in parent.Controls)
            {
                if (originalControlBounds.ContainsKey(control))
                {
                    Rectangle originalBounds = originalControlBounds[control];
                    Font originalFont = originalControlFonts[control];

                    // 调整控件位置和大小
                    control.Left = (int)(originalBounds.Left * scaleX);
                    control.Top = (int)(originalBounds.Top * scaleY);
                    control.Width = (int)(originalBounds.Width * scaleX);
                    control.Height = (int)(originalBounds.Height * scaleY);

                    // 调整字体大小
                    float newFontSize = originalFont.Size * Math.Min(scaleX, scaleY);
                    if (newFontSize > 6 && newFontSize < 72) // 限制字体大小范围
                    {
                        try
                        {
                            control.Font = new Font(originalFont.FontFamily, newFontSize, originalFont.Style);
                        }
                        catch
                        {
                            // 如果字体创建失败，保持原字体
                        }
                    }
                }

                if (control.HasChildren)
                {
                    ResizeControls(control);
                }
            }
        }

        #endregion
    }
}
