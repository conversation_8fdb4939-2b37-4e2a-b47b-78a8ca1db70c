using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsAnalysisSystem.Core;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    public partial class StaticRockMechanicsForm : Form
    {
        private DataTable mechanicsData;
        private List<RockMechanicsDataPoint> dataPoints;
        private RockMechanicsCalculator calculator;

        // 自适应缩放相关字段
        private readonly Size originalFormSize = new Size(1400, 900);
        private readonly Dictionary<Control, Rectangle> originalControlBounds = new Dictionary<Control, Rectangle>();
        private readonly Dictionary<Control, Font> originalControlFonts = new Dictionary<Control, Font>();

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeData();
            InitializeChart();
            InitializeScaling();
        }

        private void InitializeData()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("序号", typeof(int));
            mechanicsData.Columns.Add("密度(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度(m/s)", typeof(double));
            mechanicsData.Columns.Add("杨氏模量(GPa)", typeof(double));
            mechanicsData.Columns.Add("泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数", typeof(double));

            dataPoints = new List<RockMechanicsDataPoint>();
            calculator = new RockMechanicsCalculator();

            // 绑定数据表到DataGridView
            dgvMechanicsData.DataSource = mechanicsData;
            dgvMechanicsData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
        }

        private void InitializeChart()
        {
            // 检查图表控件是否存在
            if (chartBrittleness != null)
            {
                chartBrittleness.Series.Clear();
                chartBrittleness.ChartAreas.Clear();

                // 创建图表区域
                ChartArea chartArea = new ChartArea("MainArea");
                chartArea.AxisX.Title = "数据点";
                chartArea.AxisY.Title = "脆性指数";
                chartArea.BackColor = Color.FromArgb(45, 45, 45);
                chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                chartArea.AxisX.TitleForeColor = Color.White;
                chartArea.AxisY.TitleForeColor = Color.White;
                chartBrittleness.ChartAreas.Add(chartArea);

                // 创建数据系列
                Series series = new Series("脆性指数");
                series.ChartType = SeriesChartType.Line;
                series.Color = Color.FromArgb(0, 120, 215);
                series.BorderWidth = 2;
                series.MarkerStyle = MarkerStyle.Circle;
                series.MarkerSize = 6;
                series.MarkerColor = Color.FromArgb(0, 120, 215);
                chartBrittleness.Series.Add(series);

                chartBrittleness.BackColor = Color.FromArgb(33, 33, 33);
            }
        }

        private void btnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                double density = Convert.ToDouble(txtDensity.Text);
                double vp = Convert.ToDouble(txtVp.Text);
                double vs = Convert.ToDouble(txtVs.Text);

                var result = calculator.CalculateBrittleness(density, vp, vs);

                // 添加到数据表
                int rowIndex = mechanicsData.Rows.Count + 1;
                mechanicsData.Rows.Add(rowIndex, density, vp, vs,
                    result.YoungModulus, result.PoissonRatio, result.BrittlenessIndex);

                // 添加到数据点列表
                var dataPoint = new RockMechanicsDataPoint
                {
                    Density = density,
                    VpVelocity = vp,
                    VsVelocity = vs,
                    YoungModulus = result.YoungModulus,
                    PoissonRatio = result.PoissonRatio,
                    BrittlenessIndex = result.BrittlenessIndex
                };
                dataPoints.Add(dataPoint);

                // 更新图表
                UpdateChart();

                // 显示计算结果
                lblCalculationResult.Text = $"计算完成！脆性指数: {result.BrittlenessIndex:F3}";

                // 清空输入框
                ClearInputs();

                LoggingService.Instance.Info($"计算脆性指数: 密度={density}, Vp={vp}, Vs={vs}, 结果={result.BrittlenessIndex:F3}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"计算脆性指数失败: {ex.Message}");
            }
        }

        private void UpdateChart()
        {
            // 检查图表控件和系列是否存在
            if (chartBrittleness != null && chartBrittleness.Series.Count > 0)
            {
                chartBrittleness.Series["脆性指数"].Points.Clear();

                for (int i = 0; i < dataPoints.Count; i++)
                {
                    chartBrittleness.Series["脆性指数"].Points.AddXY(i + 1, dataPoints[i].BrittlenessIndex);
                }

                chartBrittleness.Invalidate();
            }
        }

        private void ClearInputs()
        {
            txtDensity.Clear();
            txtVp.Clear();
            txtVs.Clear();
            txtDensity.Focus();
        }

        private void btnClearData_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要清空所有数据吗？", "确认清空",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                mechanicsData.Clear();
                dataPoints.Clear();
                if (chartBrittleness != null && chartBrittleness.Series.Count > 0)
                {
                    chartBrittleness.Series["脆性指数"].Points.Clear();
                }
                lblCalculationResult.Text = "数据已清空";
                LoggingService.Instance.Info("清空所有计算数据");
            }
        }

        private void btnImportData_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "Excel文件|*.xlsx;*.xls|CSV文件|*.csv|所有文件|*.*";
                openFileDialog.Title = "选择要导入的数据文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 这里可以添加数据导入逻辑
                    MessageBox.Show("数据导入功能正在开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"导入数据失败: {ex.Message}");
            }
        }

        private void btnExportData_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件|*.xlsx|CSV文件|*.csv";
                saveFileDialog.Title = "保存数据文件";
                saveFileDialog.FileName = $"静态岩石力学参数分析结果_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 这里可以添加数据导出逻辑
                    MessageBox.Show("数据导出功能正在开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"导出数据失败: {ex.Message}");
            }
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (mechanicsData.Rows.Count > 0)
            {
                DialogResult result = MessageBox.Show("当前有未保存的数据，确定要关闭吗？", "确认关闭",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            LoggingService.Instance.Info("关闭静态岩石力学参数分析窗体");
        }

        // 添加缺失的事件处理方法
        private void btnLogout_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要退出登录吗？", "确认退出",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void btnEmergencyExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void btnViewComparison_Click(object sender, EventArgs e)
        {
            MessageBox.Show("对比图功能正在开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnEnhancedAnalysis_Click(object sender, EventArgs e)
        {
            try
            {
                // 这里将集成新系统的特色分析算法
                MessageBox.Show("增强分析算法功能包括：\n\n" +
                    "1. 多参数综合分析\n" +
                    "2. 智能数据验证\n" +
                    "3. 高级统计分析\n" +
                    "4. 机器学习预测\n" +
                    "5. 可视化对比分析\n\n" +
                    "功能正在完善中...",
                    "增强分析算法", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoggingService.Instance.Info("用户访问增强分析算法功能");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动增强分析功能失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"启动增强分析功能失败: {ex.Message}");
            }
        }

        #region 自适应缩放功能

        /// <summary>
        /// 初始化缩放功能，记录原始控件位置和字体
        /// </summary>
        private void InitializeScaling()
        {
            // 记录所有控件的原始位置和字体
            RecordOriginalBounds(this);
        }

        /// <summary>
        /// 递归记录控件的原始位置和字体
        /// </summary>
        private void RecordOriginalBounds(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                originalControlBounds[control] = control.Bounds;
                originalControlFonts[control] = control.Font;

                if (control.HasChildren)
                {
                    RecordOriginalBounds(control);
                }
            }
        }

        /// <summary>
        /// 窗体大小改变事件处理
        /// </summary>
        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            if (originalControlBounds.Count > 0)
            {
                ResizeControls(this);
            }
        }

        /// <summary>
        /// 递归调整控件大小和位置
        /// </summary>
        private void ResizeControls(Control parent)
        {
            float scaleX = (float)this.ClientSize.Width / originalFormSize.Width;
            float scaleY = (float)this.ClientSize.Height / originalFormSize.Height;

            foreach (Control control in parent.Controls)
            {
                if (originalControlBounds.ContainsKey(control))
                {
                    Rectangle originalBounds = originalControlBounds[control];
                    Font originalFont = originalControlFonts[control];

                    // 调整控件位置和大小
                    control.Left = (int)(originalBounds.Left * scaleX);
                    control.Top = (int)(originalBounds.Top * scaleY);
                    control.Width = (int)(originalBounds.Width * scaleX);
                    control.Height = (int)(originalBounds.Height * scaleY);

                    // 调整字体大小
                    float newFontSize = originalFont.Size * Math.Min(scaleX, scaleY);
                    if (newFontSize > 6 && newFontSize < 72) // 限制字体大小范围
                    {
                        try
                        {
                            control.Font = new Font(originalFont.FontFamily, newFontSize, originalFont.Style);
                        }
                        catch
                        {
                            // 如果字体创建失败，保持原字体
                        }
                    }
                }

                if (control.HasChildren)
                {
                    ResizeControls(control);
                }
            }
        }

        #endregion
    }
}
