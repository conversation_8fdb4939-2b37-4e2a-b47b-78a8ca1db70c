using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;
using Newtonsoft.Json;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    /// <summary>
    /// 简化版对比图显示窗体
    /// </summary>
    public partial class SimpleComparisonChartForm : Form
    {
        private Chart chartComparison;
        private Button btnClose;
        private Button btnSaveImage;
        private Label lblTitle;
        private Panel pnlControls;

        public SimpleComparisonChartForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.chartComparison = new Chart();
            this.btnClose = new Button();
            this.btnSaveImage = new Button();
            this.lblTitle = new Label();
            this.pnlControls = new Panel();
            ((System.ComponentModel.ISupportInitialize)(this.chartComparison)).BeginInit();
            this.pnlControls.SuspendLayout();
            this.SuspendLayout();

            // 
            // chartComparison
            // 
            this.chartComparison.BackColor = Color.FromArgb(33, 33, 33);
            this.chartComparison.BorderlineColor = Color.Gray;
            this.chartComparison.BorderlineDashStyle = ChartDashStyle.Solid;
            this.chartComparison.Location = new Point(12, 60);
            this.chartComparison.Name = "chartComparison";
            this.chartComparison.Size = new Size(1160, 680);
            this.chartComparison.TabIndex = 0;

            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Microsoft YaHei UI", 14F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(12, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(300, 25);
            this.lblTitle.TabIndex = 1;
            this.lblTitle.Text = "脆性指数计算方法对比图";

            // 
            // pnlControls
            // 
            this.pnlControls.BackColor = Color.FromArgb(45, 45, 45);
            this.pnlControls.Controls.Add(this.btnSaveImage);
            this.pnlControls.Controls.Add(this.btnClose);
            this.pnlControls.Location = new Point(12, 750);
            this.pnlControls.Name = "pnlControls";
            this.pnlControls.Size = new Size(1160, 50);
            this.pnlControls.TabIndex = 2;

            // 
            // btnSaveImage
            // 
            this.btnSaveImage.BackColor = Color.FromArgb(50, 50, 50);
            this.btnSaveImage.FlatAppearance.BorderColor = Color.Cyan;
            this.btnSaveImage.FlatStyle = FlatStyle.Flat;
            this.btnSaveImage.ForeColor = Color.LightSkyBlue;
            this.btnSaveImage.Location = new Point(10, 10);
            this.btnSaveImage.Name = "btnSaveImage";
            this.btnSaveImage.Size = new Size(100, 30);
            this.btnSaveImage.TabIndex = 0;
            this.btnSaveImage.Text = "保存图像";
            this.btnSaveImage.UseVisualStyleBackColor = false;
            this.btnSaveImage.Click += new EventHandler(this.BtnSaveImage_Click);

            // 
            // btnClose
            // 
            this.btnClose.BackColor = Color.FromArgb(50, 50, 50);
            this.btnClose.FlatAppearance.BorderColor = Color.Cyan;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.ForeColor = Color.LightSkyBlue;
            this.btnClose.Location = new Point(1050, 10);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(100, 30);
            this.btnClose.TabIndex = 1;
            this.btnClose.Text = "关闭";
            this.btnClose.UseVisualStyleBackColor = false;
            this.btnClose.Click += new EventHandler(this.BtnClose_Click);

            // 
            // SimpleComparisonChartForm
            // 
            this.AutoScaleDimensions = new SizeF(11F, 24F);
            this.AutoScaleMode = AutoScaleMode.Dpi;
            this.BackColor = Color.FromArgb(33, 33, 33);
            this.ClientSize = new Size(1184, 812);
            this.Controls.Add(this.pnlControls);
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.chartComparison);
            this.ForeColor = Color.White;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SimpleComparisonChartForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "脆性指数对比图";
            ((System.ComponentModel.ISupportInitialize)(this.chartComparison)).EndInit();
            this.pnlControls.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

            // 初始化图表
            InitializeChart();
        }

        private void InitializeChart()
        {
            // 清除现有内容
            chartComparison.Series.Clear();
            chartComparison.ChartAreas.Clear();
            chartComparison.Legends.Clear();

            // 创建图表区域
            ChartArea chartArea = new ChartArea("ComparisonArea");
            chartArea.BackColor = Color.FromArgb(45, 45, 45);
            chartArea.AxisX.Title = "脆性指数";
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisY.IsReversed = true; // 深度轴反向显示
            chartComparison.ChartAreas.Add(chartArea);

            // 创建图例
            Legend legend = new Legend("ComparisonLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            legend.Docking = Docking.Right;
            legend.Alignment = StringAlignment.Center;
            chartComparison.Legends.Add(legend);
        }

        public void LoadComparisonData(string mineralDataPath, string staticDataPath, bool hasMineralData, bool hasStaticData)
        {
            try
            {
                int loadedSeries = 0;

                // 加载矿物组分法数据
                if (hasMineralData && File.Exists(mineralDataPath))
                {
                    LoadDataFromFile(mineralDataPath, Color.Blue, "矿物组分法");
                    loadedSeries++;
                }

                // 加载静态岩石力学参数法数据
                if (hasStaticData && File.Exists(staticDataPath))
                {
                    LoadDataFromFile(staticDataPath, Color.Cyan, "静态岩石力学参数法");
                    loadedSeries++;
                }

                // 更新标题
                if (loadedSeries > 0)
                {
                    int totalPoints = chartComparison.Series.Sum(s => s.Points.Count);
                    lblTitle.Text = $"脆性指数计算方法对比图 - 已加载 {loadedSeries} 个系统的数据，共 {totalPoints} 个数据点";
                }
                else
                {
                    lblTitle.Text = "脆性指数计算方法对比图 - 暂无数据";
                }

                LoggingService.Instance.Info($"对比图数据加载完成，共 {loadedSeries} 个系列");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载对比数据失败: {ex.Message}");
                MessageBox.Show($"加载对比数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadDataFromFile(string filePath, Color seriesColor, string seriesName)
        {
            try
            {
                string jsonContent = File.ReadAllText(filePath);
                dynamic data = JsonConvert.DeserializeObject(jsonContent);

                if (data?.DataPoints != null)
                {
                    // 创建数据系列
                    Series series = new Series(seriesName);
                    series.ChartType = seriesName.Contains("静态") ? SeriesChartType.Spline : SeriesChartType.Line;
                    series.Color = seriesColor;
                    series.BorderWidth = 2;
                    
                    if (seriesName.Contains("静态"))
                    {
                        series.MarkerStyle = MarkerStyle.None; // 静态岩石力学参数法不显示数据点
                    }
                    else
                    {
                        series.MarkerStyle = MarkerStyle.Circle; // 矿物组分法显示圆形数据点
                        series.MarkerSize = 6;
                        series.MarkerColor = seriesColor;
                    }

                    // 添加数据点
                    foreach (var point in data.DataPoints)
                    {
                        double brittleIndex = (double)point.BrittleIndex;
                        double depth = (double)point.TopDepth;

                        if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                        {
                            series.Points.AddXY(brittleIndex, depth);
                        }
                    }

                    chartComparison.Series.Add(series);
                    LoggingService.Instance.Info($"已加载 {seriesName} 数据，共 {series.Points.Count} 个数据点");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载文件 {filePath} 失败: {ex.Message}");
            }
        }

        private void BtnSaveImage_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "PNG图像|*.png|JPEG图像|*.jpg|所有文件|*.*";
                saveDialog.Title = "保存对比图";
                saveDialog.FileName = $"脆性指数对比图_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    chartComparison.SaveImage(saveDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show($"图像已保存到: {saveDialog.FileName}", "保存成功", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoggingService.Instance.Info($"对比图已保存到: {saveDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"保存对比图失败: {ex.Message}");
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
